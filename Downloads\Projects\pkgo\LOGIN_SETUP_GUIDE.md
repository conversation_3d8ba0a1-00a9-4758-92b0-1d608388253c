# Pokémon GO Bot Login Setup Guide

## Overview
Your bot now has automatic login functionality, but you need to create template images for it to work properly.

## Required Template Images

You need to create these template images by taking screenshots of your Pokémon GO app:

### 1. <PERSON>gin <PERSON> (`templates/login_button.png`)
- Take a screenshot when you see the main Pokémon GO screen with a "Login" or "Sign In" button
- Crop just the button area and save as `templates/login_button.png`

### 2. Username Field (`templates/username_field.png`)
- Screenshot the login form showing the username/email input field
- <PERSON>rop just the input field area and save as `templates/username_field.png`

### 3. Password Field (`templates/password_field.png`)
- Screenshot the login form showing the password input field
- <PERSON><PERSON> just the password field area and save as `templates/password_field.png`

### 4. Sign In Button (`templates/signin_button.png`)
- Screenshot the "Sign In" or "Login" button on the login form
- Crop just the button and save as `templates/signin_button.png`

### 5. Google Login (`templates/google_login.png`)
- If you use Google login, screenshot the "Sign in with Google" button
- C<PERSON> and save as `templates/google_login.png`

### 6. Facebook Login (`templates/facebook_login.png`)
- If you use Facebook login, screenshot the "Continue with Facebook" button
- Crop and save as `templates/facebook_login.png`

## How to Create Template Images

1. **Open Pokémon GO** on your device
2. **Navigate to the login screen**
3. **Take a screenshot** (usually Power + Volume Down)
4. **Transfer the screenshot** to your computer
5. **Use an image editor** (Paint, GIMP, Photoshop, etc.) to:
   - Open the screenshot
   - Select/crop just the button or field you want
   - Save as PNG in the `templates/` folder with the correct name

## Important Notes

- **Template images must be exact matches** - they should be cropped precisely
- **Test with different screen states** - buttons may look different when pressed
- **Consider screen resolution** - templates work best on the same device/resolution they were created on
- **Keep templates small** - crop tightly around the element you want to detect

## Testing Your Templates

After creating the templates, you can test them by:

1. Setting `AUTO_LOGIN_ENABLED = True` in `bot.py`
2. Running the bot: `python bot.py`
3. Watching the console output to see if templates are found

## Security Warning

⚠️ **IMPORTANT**: Your login credentials are currently stored in plain text in `bot.py`. Consider:
- Using environment variables for credentials
- Being aware that automated gameplay may violate Pokémon GO's Terms of Service
- Understanding the risks of account suspension

## Troubleshooting

If login fails:
1. Check that template images exist in the `templates/` folder
2. Verify template images are clear and properly cropped
3. Ensure your device is authorized for ADB debugging
4. Check console output for specific error messages
5. You may need to handle 2FA or captcha manually

## Alternative: Manual Login

If automatic login is too complex, you can:
1. Set `AUTO_LOGIN_ENABLED = False`
2. Manually log into Pokémon GO before running the bot
3. The bot will start automation from whatever screen you're on
