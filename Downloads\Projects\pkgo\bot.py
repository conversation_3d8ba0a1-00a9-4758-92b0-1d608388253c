import cv2
import numpy as np
from ppadb.client import Client as AdbClient
import time
import random
import math

# --- Configuration ---
# You can adjust these values
ADB_HOST = "127.0.0.1"
ADB_PORT = 5037
MATCH_THRESHOLD = 0.8 # How similar the image must be. 0.8 is 80%.
EVOLVE_POKEMON_ENABLED = False # Set to True to enable evolution

# --- Login Configuration ---
POKEMON_GO_USERNAME = "Jeheuty"
POKEMON_GO_PASSWORD = "Z0mgg.oo.gle4577!"
AUTO_LOGIN_ENABLED = True  # Set to True to enable automatic login
TARGET_LOCATION_GPS = (40.7128, -74.0060) # Example: New York City (latitude, longitude)

# --- Login Template Paths (YOU MUST CREATE THESE) ---
LOGIN_BUTTON_TEMPLATE_PATH = 'templates/login_button.png'
USERNAME_FIELD_TEMPLATE_PATH = 'templates/username_field.png'
PASSWORD_FIELD_TEMPLATE_PATH = 'templates/password_field.png'
SIGNIN_BUTTON_TEMPLATE_PATH = 'templates/signin_button.png'
GOOGLE_LOGIN_TEMPLATE_PATH = 'templates/google_login.png'
FACEBOOK_LOGIN_TEMPLATE_PATH = 'templates/facebook_login.png'


# --- NEW: Template Paths ---
POKESTOP_TEMPLATE_PATH = 'templates/blue_pokestop.png'
PURPLE_POKESTOP_TEMPLATE_PATH = 'templates/puple_pokestop.png'
# POKEBALL_TEMPLATE_PATH = 'templates/pokeball.png' # Disabled: Missing template
# POKEMON_TEMPLATE_PATHS = [ # Disabled: Missing templates
#     'templates/pidgey.png',
#     'templates/eevee.png',
#     # Add paths to any other pokemon templates you created
# ]

# --- NEW: Templates for Evolution (USER MUST PROVIDE THESE) ---
# MAIN_MENU_BUTTON_TEMPLATE_PATH = 'templates/main_menu_button.png'
# POKEMON_BUTTON_TEMPLATE_PATH = 'templates/pokemon_button.png'
# EVOLVE_BUTTON_TEMPLATE_PATH = 'templates/evolve_button.png'
# CONFIRM_EVOLVE_BUTTON_TEMPLATE_PATH = 'templates/confirm_evolve_button.png'
# FIRST_POKEMON_TEMPLATE_PATH = 'templates/first_pokemon.png' # A template to select the first pokemon in the list

# --- NEW: Joystick Configuration ---
# Find these coordinates on your screen where the joystick is
JOYSTICK_CENTER_X = 250
JOYSTICK_CENTER_Y = 930
WALK_RADIUS = 100 # How far to "pull" the joystick

# A predefined path for the bot to follow.
# Each tuple is an (x, y) coordinate to swipe the joystick to.
# This example path makes the bot walk in a square.
WALKING_PATH = [
    (JOYSTICK_CENTER_X + WALK_RADIUS, JOYSTICK_CENTER_Y),      # Right
    (JOYSTICK_CENTER_X, JOYSTICK_CENTER_Y + WALK_RADIUS),      # Down
    (JOYSTICK_CENTER_X - WALK_RADIUS, JOYSTICK_CENTER_Y),      # Left
    (JOYSTICK_CENTER_X, JOYSTICK_CENTER_Y - WALK_RADIUS),      # Up
]

# --- ADB & Image Functions (Mostly unchanged) ---

def connect_device():
    """Connects to the ADB server and the first available device."""
    try:
        client = AdbClient(host=ADB_HOST, port=ADB_PORT)
        devices = client.devices()
        if len(devices) == 0:
            print("No devices found.")
            return None
        print(f"Connected to device: {devices[0].serial}")
        return devices[0]
    except Exception as e:
        print(f"Error connecting to device: {e}")
        return None

def take_screenshot(device):
    """Takes a screenshot and returns it as an OpenCV image."""
    try:
        result = device.screencap()
        img_np = np.frombuffer(result, np.uint8)
        image = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
        return image
    except Exception as e:
        print(f"Error taking screenshot: {e}")
        return None

def find_template(screen_image, template_path, threshold):
    """Finds a template on screen and returns its center coordinates."""
    template = cv2.imread(template_path, cv2.IMREAD_COLOR)
    if template is None:
        print(f"Could not read template: {template_path}")
        return None

    result = cv2.matchTemplate(screen_image, template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    if max_val >= threshold:
        print(f"Found '{template_path}' with confidence: {max_val:.2f}")
        template_w, template_h = template.shape[1], template.shape[0]
        center_x = max_loc[0] + template_w // 2
        center_y = max_loc[1] + template_h // 2
        return (center_x, center_y)
    
    return None

def tap(device, x, y):
    """Simulates a tap."""
    print(f"Tapping at ({x}, {y})")
    device.shell(f'input tap {x} {y}')

def swipe(device, x1, y1, x2, y2, duration_ms):
    """Simulates a swipe."""
    print(f"Swiping from ({x1}, {y1}) to ({x2}, {y2})")
    device.shell(f'input swipe {x1} {y1} {x2} {y2} {duration_ms}')

# --- Future Feature Placeholder Functions ---

def login(device, username, password):
    """
    Handles the automatic login process using template matching.
    Returns True if login appears successful, False otherwise.
    """
    print("Starting automatic login process...")

    # Take initial screenshot to see current state
    screen = take_screenshot(device)
    if screen is None:
        print("Failed to take screenshot for login")
        return False

    # Step 1: Look for login button on main screen
    login_coords = find_template(screen, LOGIN_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if login_coords:
        print("Found login button, tapping...")
        tap(device, login_coords[0], login_coords[1])
        time.sleep(3)
    else:
        print("No login button found, assuming already on login screen or logged in")

    # Step 2: Check for Google/Facebook login options
    screen = take_screenshot(device)
    google_coords = find_template(screen, GOOGLE_LOGIN_TEMPLATE_PATH, MATCH_THRESHOLD)
    facebook_coords = find_template(screen, FACEBOOK_LOGIN_TEMPLATE_PATH, MATCH_THRESHOLD)

    if google_coords:
        print("Found Google login option, using Google authentication...")
        tap(device, google_coords[0], google_coords[1])
        time.sleep(5)  # Wait for Google login page to load
        return handle_google_login(device, username, password)
    elif facebook_coords:
        print("Found Facebook login option, using Facebook authentication...")
        tap(device, facebook_coords[0], facebook_coords[1])
        time.sleep(5)  # Wait for Facebook login page to load
        return handle_facebook_login(device, username, password)

    # Step 3: Handle direct username/password login
    return handle_direct_login(device, username, password)

def handle_direct_login(device, username, password):
    """Handle direct username/password login"""
    screen = take_screenshot(device)

    # Find username field
    username_coords = find_template(screen, USERNAME_FIELD_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not username_coords:
        print("Could not find username field")
        return False

    # Tap username field and enter username
    tap(device, username_coords[0], username_coords[1])
    time.sleep(1)
    device.shell(f'input text "{username}"')
    time.sleep(1)

    # Find password field
    password_coords = find_template(screen, PASSWORD_FIELD_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not password_coords:
        print("Could not find password field")
        return False

    # Tap password field and enter password
    tap(device, password_coords[0], password_coords[1])
    time.sleep(1)
    device.shell(f'input text "{password}"')
    time.sleep(1)

    # Find and tap sign in button
    signin_coords = find_template(screen, SIGNIN_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if signin_coords:
        tap(device, signin_coords[0], signin_coords[1])
        print("Login credentials submitted, waiting for authentication...")
        time.sleep(10)  # Wait for login to process
        return True
    else:
        print("Could not find sign in button")
        return False

def handle_google_login(device, username, password):
    """Handle Google OAuth login"""
    print("Handling Google login - this may require manual intervention for 2FA")
    # Google login often requires 2FA or captcha, which is difficult to automate
    # For now, we'll wait and assume the user handles it manually
    time.sleep(30)  # Give user time to complete Google login manually
    return True

def handle_facebook_login(device, username, password):
    """Handle Facebook OAuth login"""
    print("Handling Facebook login - this may require manual intervention")
    # Similar to Google, Facebook login may require manual steps
    time.sleep(30)  # Give user time to complete Facebook login manually
    return True

def select_location(device, gps_coords):
    """
    (Placeholder) Sets the device's GPS location.
    This is a complex feature requiring a rooted device or a specific GPS spoofing app.
    The current `WALKING_PATH` is the primary way to control movement.
    """
    print("Location selection is a placeholder and has not been implemented.")
    # Future logic would go here:
    # 1. Interface with a GPS spoofing app
    # 2. Set the latitude and longitude
    return True

# --- Bot Action Functions ---

def spin_pokestop(device, coords, screen_shape):
    """Taps a pokestop, spins it, and closes it."""
    px, py = coords
    tap(device, px, py)
    time.sleep(2)
    
    screen_height, screen_width, _ = screen_shape
    swipe_x1 = screen_width // 4
    swipe_x2 = screen_width * 3 // 4
    swipe_y = screen_height // 2
    swipe(device, swipe_x1, swipe_y, swipe_x2, swipe_y, 300)
    time.sleep(3)
    
    tap(device, screen_width // 2, screen_height - 100)
    print("PokéStop spun!")

def catch_pokemon(device, coords, screen_shape):
    """Taps a pokemon and attempts to catch it."""
    px, py = coords
    tap(device, px, py)
    print("Encountering Pokémon...")
    time.sleep(5) # Wait for the catch screen to load

    # Check for the Poké Ball on the new screen to confirm we are in an encounter
    catch_screen = take_screenshot(device)
    pokeball_coords = find_template(catch_screen, POKEBALL_TEMPLATE_PATH, 0.7)
    
    if pokeball_coords:
        ball_x, ball_y = pokeball_coords
        screen_height, _, _ = screen_shape
        # Swipe from the ball up to the middle of the screen to throw
        throw_y = screen_height // 2
        print("Throwing Poké Ball!")
        swipe(device, ball_x, ball_y, ball_x, throw_y, 400)
        time.sleep(10) # Wait for catch animation to complete
        
        # Tap screen to dismiss any popups (e.g., "Gotcha!")
        tap(device, screen_shape[1] // 2, screen_shape[0] // 2)
        print("Catch sequence complete.")
        return True
    else:
        print("Could not find Poké Ball. Backing out.")
        # Press the back button if we're not on the catch screen
        device.shell('input keyevent 4') 
        return False

def evolve_pokemon(device, screen_shape):
    """Navigates menus to evolve the first available Pokémon."""
    print("Attempting to evolve a Pokémon...")

    # 1. Open Main Menu
    menu_coords = find_template(take_screenshot(device), MAIN_MENU_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not menu_coords:
        print("Could not find Main Menu button.")
        return False
    tap(device, menu_coords[0], menu_coords[1])
    time.sleep(1)

    # 2. Open Pokémon List
    pokemon_button_coords = find_template(take_screenshot(device), POKEMON_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not pokemon_button_coords:
        print("Could not find Pokémon button.")
        device.shell('input keyevent 4') # Back out of menu
        return False
    tap(device, pokemon_button_coords[0], pokemon_button_coords[1])
    time.sleep(2)

    # 3. Select the first Pokémon in the list
    # NOTE: This assumes the list is sorted so that an evolvable Pokémon is first.
    # A more robust solution would scroll the list to find a suitable Pokémon.
    # For now, we use a template to find the area of the first pokemon.
    first_pokemon_coords = find_template(take_screenshot(device), FIRST_POKEMON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not first_pokemon_coords:
        print("Could not find the first Pokémon in the list.")
        device.shell('input keyevent 4') # Back out of pokemon list
        return False
    tap(device, first_pokemon_coords[0], first_pokemon_coords[1])
    time.sleep(2)
    
    # 4. Find and tap the Evolve button
    evolve_coords = find_template(take_screenshot(device), EVOLVE_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not evolve_coords:
        print("Could not find Evolve button. This Pokémon may not be ready.")
        device.shell('input keyevent 4') # Back out of Pokémon details
        device.shell('input keyevent 4') # Back out of Pokémon list
        return False
    tap(device, evolve_coords[0], evolve_coords[1])
    time.sleep(1)

    # 5. Confirm Evolution
    confirm_coords = find_template(take_screenshot(device), CONFIRM_EVOLVE_BUTTON_TEMPLATE_PATH, MATCH_THRESHOLD)
    if not confirm_coords:
        print("Could not find Evolve confirmation button.")
        device.shell('input keyevent 4') # Back out of confirmation
        device.shell('input keyevent 4') # Back out of Pokémon details
        device.shell('input keyevent 4') # Back out of Pokémon list
        return False
    tap(device, confirm_coords[0], confirm_coords[1])
    
    print("Evolution in progress...")
    time.sleep(20) # Wait for evolution animation

    # Tap to dismiss the evolution screen
    tap(device, screen_shape[1] // 2, screen_shape[0] // 2)
    time.sleep(2)
    # Go back to the main screen
    device.shell('input keyevent 4')
    device.shell('input keyevent 4')
    print("Evolution sequence complete.")
    return True

def walk_around(device, path, current_index):
    """
    Simulates walking by following a predefined path or wandering randomly if the path is empty.
    Returns the next index in the path.
    """
    if not path:
        # Fallback to random walking if no path is defined
        print("No walking path defined. Walking randomly.")
        angle = random.uniform(0, 2 * math.pi)
        end_x = int(JOYSTICK_CENTER_X + WALK_RADIUS * math.cos(angle))
        end_y = int(JOYSTICK_CENTER_Y + WALK_RADIUS * math.sin(angle))
        walk_duration = random.randint(500, 1500)
        print(f"Walking randomly for {walk_duration}ms...")
        swipe(device, JOYSTICK_CENTER_X, JOYSTICK_CENTER_Y, end_x, end_y, walk_duration)
        return 0 # Return a default index

    # Follow the predefined path
    target_coords = path[current_index]
    end_x, end_y = target_coords
    
    walk_duration = random.randint(1000, 2000) # Walk for 1 to 2 seconds
    print(f"Walking to path index {current_index} at ({end_x}, {end_y}) for {walk_duration}ms...")
    swipe(device, JOYSTICK_CENTER_X, JOYSTICK_CENTER_Y, end_x, end_y, walk_duration)
    
    next_index = (current_index + 1) % len(path)
    return next_index

# --- The Main Bot Logic ---

def main():
    """The main loop of the bot."""
    device = connect_device()
    if not device:
        return

    # --- Automatic Login ---
    if AUTO_LOGIN_ENABLED:
        print("Attempting automatic login...")
        login_successful = login(device, POKEMON_GO_USERNAME, POKEMON_GO_PASSWORD)
        if not login_successful:
            print("Automatic login failed. You may need to login manually or check your credentials.")
            print("Continuing with bot operations...")
        else:
            print("Login completed successfully!")

        # Optional: Set location (requires GPS spoofing app)
        # select_location(device, TARGET_LOCATION_GPS)

    print("Bot starting... Press Ctrl+C to stop.")
    current_path_index = 0
    try:
        while True:
            action_taken = False
            screen = take_screenshot(device)
            if screen is None:
                time.sleep(2)
                continue

            # PRIORITY 1: CATCH POKÉMON (Feature disabled temporarily)
            # The template images for Pokémon are not available yet.
            # for pokemon_path in POKEMON_TEMPLATE_PATHS:
            #     pokemon_coords = find_template(screen, pokemon_path, MATCH_THRESHOLD)
            #     if pokemon_coords:
            #         catch_pokemon(device, pokemon_coords, screen.shape)
            #         action_taken = True
            #         break # Exit pokemon loop after dealing with one
            # if action_taken:
            #     time.sleep(5) # Wait a bit after the action
            #     continue

            # PRIORITY 2: EVOLVE POKÉMON
            if EVOLVE_POKEMON_ENABLED:
                if evolve_pokemon(device, screen.shape):
                    action_taken = True
            if action_taken:
                time.sleep(5)
                continue

            # PRIORITY 3: SPIN POKÉSTOPS
            blue_pokestop_coords = find_template(screen, POKESTOP_TEMPLATE_PATH, MATCH_THRESHOLD)
            if blue_pokestop_coords:
                print("Found a blue PokéStop, spinning...")
                spin_pokestop(device, blue_pokestop_coords, screen.shape)
                action_taken = True
            else:
                # If no blue ones, check for purple ones to know we are near a spun one
                purple_pokestop_coords = find_template(screen, PURPLE_POKESTOP_TEMPLATE_PATH, MATCH_THRESHOLD)
                if purple_pokestop_coords:
                    print("Found a purple PokéStop, skipping.")
                    # We still want to walk away from it
                    action_taken = False
            
            # PRIORITY 4: WALK AROUND
            if not action_taken:
                current_path_index = walk_around(device, WALKING_PATH, current_path_index)

            # Wait a random amount of time before the next cycle
            sleep_duration = random.uniform(5, 8)
            print(f"Sleeping for {sleep_duration:.2f} seconds.")
            time.sleep(sleep_duration)

    except KeyboardInterrupt:
        print("\nBot stopped by user.")

if __name__ == "__main__":
    main()