import cv2
import numpy as np
from ppadb.client import Client as AdbClient
import time
import random
import math

# --- Configuration ---
# You can adjust these values
ADB_HOST = "127.0.0.1"
ADB_PORT = 5037
MATCH_THRESHOLD = 0.8 # How similar the image must be. 0.8 is 80%.

# --- NEW: Template Paths ---
POKESTOP_TEMPLATE_PATH = 'templates/pokestop_blue.png'
POKEBALL_TEMPLATE_PATH = 'templates/pokeball.png'
POKEMON_TEMPLATE_PATHS = [
    'templates/pidgey.png',
    'templates/eevee.png',
    # Add paths to any other pokemon templates you created
]

# --- NEW: Joystick Configuration ---
# Find these coordinates on your screen where the joystick is
JOYSTICK_CENTER_X = 250
JOYSTICK_CENTER_Y = 930
WALK_RADIUS = 100 # How far to "pull" the joystick

# --- ADB & Image Functions (Mostly unchanged) ---

def connect_device():
    """Connects to the ADB server and the first available device."""
    try:
        client = AdbClient(host=ADB_HOST, port=ADB_PORT)
        devices = client.devices()
        if len(devices) == 0:
            print("No devices found.")
            return None
        print(f"Connected to device: {devices[0].serial}")
        return devices[0]
    except Exception as e:
        print(f"Error connecting to device: {e}")
        return None

def take_screenshot(device):
    """Takes a screenshot and returns it as an OpenCV image."""
    try:
        result = device.screencap()
        img_np = np.frombuffer(result, np.uint8)
        image = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
        return image
    except Exception as e:
        print(f"Error taking screenshot: {e}")
        return None

def find_template(screen_image, template_path, threshold):
    """Finds a template on screen and returns its center coordinates."""
    template = cv2.imread(template_path, cv2.IMREAD_COLOR)
    if template is None:
        print(f"Could not read template: {template_path}")
        return None

    result = cv2.matchTemplate(screen_image, template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    if max_val >= threshold:
        print(f"Found '{template_path}' with confidence: {max_val:.2f}")
        template_w, template_h = template.shape[1], template.shape[0]
        center_x = max_loc[0] + template_w // 2
        center_y = max_loc[1] + template_h // 2
        return (center_x, center_y)
    
    return None

def tap(device, x, y):
    """Simulates a tap."""
    print(f"Tapping at ({x}, {y})")
    device.shell(f'input tap {x} {y}')

def swipe(device, x1, y1, x2, y2, duration_ms):
    """Simulates a swipe."""
    print(f"Swiping from ({x1}, {y1}) to ({x2}, {y2})")
    device.shell(f'input swipe {x1} {y1} {x2} {y2} {duration_ms}')

# --- NEW: Bot Action Functions ---

def spin_pokestop(device, coords, screen_shape):
    """Taps a pokestop, spins it, and closes it."""
    px, py = coords
    tap(device, px, py)
    time.sleep(2)
    
    screen_height, screen_width, _ = screen_shape
    swipe_x1 = screen_width // 4
    swipe_x2 = screen_width * 3 // 4
    swipe_y = screen_height // 2
    swipe(device, swipe_x1, swipe_y, swipe_x2, swipe_y, 300)
    time.sleep(3)
    
    tap(device, screen_width // 2, screen_height - 100)
    print("PokéStop spun!")

def catch_pokemon(device, coords, screen_shape):
    """Taps a pokemon and attempts to catch it."""
    px, py = coords
    tap(device, px, py)
    print("Encountering Pokémon...")
    time.sleep(5) # Wait for the catch screen to load

    # Check for the Poké Ball on the new screen to confirm we are in an encounter
    catch_screen = take_screenshot(device)
    pokeball_coords = find_template(catch_screen, POKEBALL_TEMPLATE_PATH, 0.7)
    
    if pokeball_coords:
        ball_x, ball_y = pokeball_coords
        screen_height, _, _ = screen_shape
        # Swipe from the ball up to the middle of the screen to throw
        throw_y = screen_height // 2
        print("Throwing Poké Ball!")
        swipe(device, ball_x, ball_y, ball_x, throw_y, 400)
        time.sleep(10) # Wait for catch animation to complete
        
        # Tap screen to dismiss any popups (e.g., "Gotcha!")
        tap(device, screen_shape[1] // 2, screen_shape[0] // 2)
        print("Catch sequence complete.")
        return True
    else:
        print("Could not find Poké Ball. Backing out.")
        # Press the back button if we're not on the catch screen
        device.shell('input keyevent 4') 
        return False

def walk_around(device):
    """Simulates a short walk using the on-screen joystick."""
    # Choose a random direction (angle in radians)
    angle = random.uniform(0, 2 * math.pi)
    
    # Calculate the end point of the swipe
    end_x = int(JOYSTICK_CENTER_X + WALK_RADIUS * math.cos(angle))
    end_y = int(JOYSTICK_CENTER_Y + WALK_RADIUS * math.sin(angle))
    
    # Perform the swipe for a random duration to simulate walking
    walk_duration = random.randint(500, 1500) # Walk for 0.5 to 1.5 seconds
    print(f"Walking for {walk_duration}ms...")
    swipe(device, JOYSTICK_CENTER_X, JOYSTICK_CENTER_Y, end_x, end_y, walk_duration)

# --- The Main Bot Logic ---

def main():
    """The main loop of the bot."""
    device = connect_device()
    if not device:
        return

    print("Bot starting... Press Ctrl+C to stop.")
    try:
        while True:
            action_taken = False
            screen = take_screenshot(device)
            if screen is None:
                time.sleep(2)
                continue

            # PRIORITY 1: CATCH POKÉMON
            for pokemon_path in POKEMON_TEMPLATE_PATHS:
                pokemon_coords = find_template(screen, pokemon_path, MATCH_THRESHOLD)
                if pokemon_coords:
                    catch_pokemon(device, pokemon_coords, screen.shape)
                    action_taken = True
                    break # Exit pokemon loop after dealing with one
            if action_taken:
                time.sleep(5) # Wait a bit after the action
                continue

            # PRIORITY 2: SPIN POKÉSTOPS
            pokestop_coords = find_template(screen, POKESTOP_TEMPLATE_PATH, MATCH_THRESHOLD)
            if pokestop_coords:
                spin_pokestop(device, pokestop_coords, screen.shape)
                action_taken = True
            
            # PRIORITY 3: WALK AROUND
            if not action_taken:
                walk_around(device)

            # Wait a random amount of time before the next cycle
            sleep_duration = random.uniform(5, 8)
            print(f"Sleeping for {sleep_duration:.2f} seconds.")
            time.sleep(sleep_duration)

    except KeyboardInterrupt:
        print("\nBot stopped by user.")

if __name__ == "__main__":
    main()