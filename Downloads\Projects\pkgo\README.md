# Pokémon GO Bot (okgo)

An automated Pokémon GO bot that can login automatically and control your character using computer vision and ADB.

## Features

- **Automatic Login**: Logs into Pokémon GO automatically using your credentials
- **Smart Gameplay**: Catches Pokémon, spins PokéStops, and walks around automatically
- **Template Matching**: Uses computer vision to identify game elements
- **ADB Integration**: Controls your Android device remotely
- **Configurable**: Easy to customize behavior and settings

## Quick Start

1. **Setup your Android device**:
   - Enable Developer Options and USB Debugging
   - Connect via USB cable

2. **Run the bot**:
   ```bash
   python run_bot.py
   ```

   This will:
   - Set up ADB connection automatically
   - Handle device authorization
   - Check for required template files
   - Launch the bot with auto-login

## Manual Setup

If you prefer manual setup:

1. **Configure credentials** in `bot.py`:
   ```python
   POKEMON_GO_USERNAME = "your_username"
   POKEMON_GO_PASSWORD = "your_password"
   AUTO_LOGIN_ENABLED = True
   ```

2. **Create template images** (see `LOGIN_SETUP_GUIDE.md`)

3. **Set up ADB**:
   ```bash
   python setup_adb.py
   ```

4. **Run the bot**:
   ```bash
   python bot.py
   ```

## Important Notes

⚠️ **Security Warning**: This bot stores credentials in plain text. Use at your own risk.

⚠️ **Terms of Service**: Automated gameplay may violate Pokémon GO's Terms of Service and could result in account suspension.

⚠️ **Use Responsibly**: This is for educational purposes. Be aware of the risks involved.

## Files

- `bot.py` - Main bot logic with auto-login
- `run_bot.py` - Easy launcher script
- `setup_adb.py` - ADB connection helper
- `LOGIN_SETUP_GUIDE.md` - Guide for creating login templates
- `templates/` - Directory for template images

## Requirements

- Python 3.6+
- Android device with USB debugging enabled
- ADB (Android Debug Bridge)
- Required Python packages: `opencv-python`, `numpy`, `pure-python-adb`

## Installation

```bash
pip install opencv-python numpy pure-python-adb
```
