#!/usr/bin/env python3
"""
Pokémon GO Bot Launcher
This script sets up ADB connection and launches the bot with automatic login.
"""

import subprocess
import sys
import time
import os

def run_setup():
    """Run the ADB setup script"""
    print("Setting up ADB connection...")
    try:
        result = subprocess.run([sys.executable, "setup_adb.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("ADB setup failed")
        return False
    except FileNotFoundError:
        print("setup_adb.py not found")
        return False

def check_templates():
    """Check if required template files exist"""
    required_templates = [
        'templates/blue_pokestop.png',
        'templates/purple_pokestop.png'
    ]
    
    login_templates = [
        'templates/login_button.png',
        'templates/username_field.png', 
        'templates/password_field.png',
        'templates/signin_button.png'
    ]
    
    missing_required = []
    missing_login = []
    
    for template in required_templates:
        if not os.path.exists(template):
            missing_required.append(template)
    
    for template in login_templates:
        if not os.path.exists(template):
            missing_login.append(template)
    
    if missing_required:
        print("✗ Missing required template files:")
        for template in missing_required:
            print(f"  - {template}")
        return False
    
    if missing_login:
        print("⚠ Missing login template files (auto-login will be disabled):")
        for template in missing_login:
            print(f"  - {template}")
        print("See LOGIN_SETUP_GUIDE.md for instructions on creating these")
        
        # Disable auto-login if templates are missing
        disable_auto_login()
    
    print("✓ Template check complete")
    return True

def disable_auto_login():
    """Disable auto-login in bot.py if templates are missing"""
    try:
        with open('bot.py', 'r') as f:
            content = f.read()
        
        # Replace AUTO_LOGIN_ENABLED = True with False
        content = content.replace('AUTO_LOGIN_ENABLED = True', 'AUTO_LOGIN_ENABLED = False')
        
        with open('bot.py', 'w') as f:
            f.write(content)
        
        print("✓ Auto-login disabled due to missing templates")
    except Exception as e:
        print(f"⚠ Could not disable auto-login: {e}")

def run_bot():
    """Launch the main bot"""
    print("\n=== Starting Pokémon GO Bot ===")
    print("Press Ctrl+C to stop the bot")
    print("=" * 40)
    
    try:
        subprocess.run([sys.executable, "bot.py"], check=True)
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"Bot exited with error code {e.returncode}")
    except Exception as e:
        print(f"Error running bot: {e}")

def main():
    """Main launcher function"""
    print("=== Pokémon GO Bot Launcher ===\n")
    
    # Check if we're in the right directory
    if not os.path.exists('bot.py'):
        print("✗ bot.py not found in current directory")
        print("Please run this script from the pkgo project directory")
        sys.exit(1)
    
    # Run ADB setup
    if not run_setup():
        print("✗ ADB setup failed. Please fix the issues and try again.")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    
    # Check templates
    if not check_templates():
        print("✗ Template check failed. Please create the required template files.")
        print("See the templates/ directory and README.md for instructions.")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    
    # Launch bot
    run_bot()

if __name__ == "__main__":
    main()
