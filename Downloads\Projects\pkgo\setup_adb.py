#!/usr/bin/env python3
"""
ADB Setup Helper Script
This script helps automate the ADB connection process for the Pokémon GO bot.
"""

import subprocess
import time
import sys

def run_command(command):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_adb_installed():
    """Check if ADB is installed and accessible"""
    success, stdout, stderr = run_command("adb version")
    if success:
        print("✓ ADB is installed and accessible")
        return True
    else:
        print("✗ ADB is not installed or not in PATH")
        print("Please install Android SDK Platform Tools")
        return False

def restart_adb_server():
    """Restart the ADB server"""
    print("Restarting ADB server...")
    
    # Kill existing server
    success, stdout, stderr = run_command("adb kill-server")
    if success:
        print("✓ ADB server stopped")
    else:
        print("⚠ Warning: Could not stop ADB server")
    
    time.sleep(2)
    
    # Start server
    success, stdout, stderr = run_command("adb start-server")
    if success:
        print("✓ ADB server started")
        return True
    else:
        print("✗ Failed to start ADB server")
        print(f"Error: {stderr}")
        return False

def check_device_connection():
    """Check if device is connected and authorized"""
    success, stdout, stderr = run_command("adb devices")
    
    if not success:
        print("✗ Failed to check device connection")
        return False, "unknown"
    
    lines = stdout.strip().split('\n')[1:]  # Skip header
    devices = [line.strip() for line in lines if line.strip()]
    
    if not devices:
        print("✗ No devices connected")
        return False, "none"
    
    for device in devices:
        if "unauthorized" in device:
            print("⚠ Device found but unauthorized")
            print("Please check your device for an authorization dialog")
            return False, "unauthorized"
        elif "device" in device:
            device_id = device.split()[0]
            print(f"✓ Device {device_id} is connected and authorized")
            return True, "authorized"
    
    print("⚠ Device in unknown state")
    return False, "unknown"

def wait_for_authorization(max_wait=60):
    """Wait for device authorization"""
    print(f"Waiting up to {max_wait} seconds for device authorization...")
    print("Please check your Android device for the 'Allow USB debugging?' dialog")
    print("Tap 'OK' or 'Allow' to authorize this computer")
    
    for i in range(max_wait):
        connected, status = check_device_connection()
        if connected and status == "authorized":
            return True
        
        if i % 10 == 0:  # Print reminder every 10 seconds
            remaining = max_wait - i
            print(f"Still waiting... {remaining} seconds remaining")
            print("Check your device screen for authorization dialog")
        
        time.sleep(1)
    
    print("✗ Timeout waiting for device authorization")
    return False

def main():
    """Main setup function"""
    print("=== Pokémon GO Bot ADB Setup ===\n")
    
    # Check if ADB is installed
    if not check_adb_installed():
        print("\nPlease install Android SDK Platform Tools and try again")
        sys.exit(1)
    
    # Restart ADB server
    if not restart_adb_server():
        print("\nFailed to restart ADB server")
        sys.exit(1)
    
    # Check initial device connection
    connected, status = check_device_connection()
    
    if connected:
        print("\n✓ Device is ready! You can now run the bot.")
        return
    
    if status == "unauthorized":
        if wait_for_authorization():
            print("\n✓ Device authorized successfully!")
            print("✓ Setup complete! You can now run the bot.")
        else:
            print("\n✗ Device authorization failed")
            print("Please try the following:")
            print("1. Disconnect and reconnect your USB cable")
            print("2. Enable 'USB Debugging' in Developer Options")
            print("3. Try running this script again")
            sys.exit(1)
    
    elif status == "none":
        print("\n✗ No device detected")
        print("Please:")
        print("1. Connect your Android device via USB")
        print("2. Enable 'USB Debugging' in Developer Options")
        print("3. Run this script again")
        sys.exit(1)
    
    else:
        print(f"\n⚠ Unknown device status: {status}")
        print("Please check your device connection and try again")
        sys.exit(1)

if __name__ == "__main__":
    main()
